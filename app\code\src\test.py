import pandas as pd
import joblib
import os
import sys

# 将当前文件所在的目录添加到Python的模块搜索路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from featurework import create_features

# --- Path Setup ---
# 使用绝对路径以避免在不同环境中出现问题
# app/code/src/test.py -> app/
APP_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

DATA_DIR = os.path.join(APP_ROOT, 'data')
MODEL_DIR = os.path.join(APP_ROOT, 'model')
OUTPUT_DIR = os.path.join(APP_ROOT, 'output')
TEST_FILE = os.path.join(DATA_DIR, 'test.csv')
TRAIN_FILE = os.path.join(DATA_DIR, 'train.csv')
# 不再加载单一模型
# MODEL_FILE = os.path.join(MODEL_DIR, 'lgb_model.joblib')
RESULT_FILE = os.path.join(OUTPUT_DIR, 'result.csv') # 最终结果文件

def generate_predictions():
    """
    完整的预测生成流程。
    """
    print("开始生成预测结果...")

    # 1. 加载训练和测试数据
    # 为了给测试集生成有意义的滚动窗口特征，需要利用历史数据
    print(f"从 {TEST_FILE} 和 {TRAIN_FILE} 加载数据...")
    try:
        test_df = pd.read_csv(TEST_FILE, encoding='utf-8')
        train_df = pd.read_csv(TRAIN_FILE, encoding='utf-8')
    except Exception as e:
        print(f"读取数据时出错: {e}")
        return

    # 记录测试集的股票代码和日期，以便后续筛选
    test_identifiers = test_df[['股票代码', '日期']]

    # 合并数据以进行统一的特征工程
    combined_df = pd.concat([train_df, test_df], ignore_index=True)

    # 关键修复：去除可能存在的重复行，确保每个股票在每天只有一条记录
    combined_df.drop_duplicates(subset=['股票代码', '日期'], keep='last', inplace=True)

    # 2. 特征工程
    print("对合并后的数据进行特征工程...")
    # 传递 test_df 的副本以避免 SettingWithCopyWarning
    features_df = create_features(combined_df, is_train=False)

    # 3. 筛选出测试集对应的特征数据
    # 将 test_identifiers 的列转换为 features_df 中匹配的类型
    test_identifiers['日期'] = pd.to_datetime(test_identifiers['日期'])
    features_df['日期'] = pd.to_datetime(features_df['日期'])

    # 使用内连接(merge)来精确地筛选出测试集的数据行
    latest_data = pd.merge(features_df, test_identifiers, on=['股票代码', '日期'], how='inner')

    # 3. 判断当前市场状态并加载相应模型
    # 我们假设最新的数据点能代表当前市场状态
    # 注意：在修复后，latest_data 可能包含多个股票，但它们的market_status应该是一样的
    if latest_data.empty:
        print("错误：在特征工程后，没有找到对应的测试集数据。请检查数据和特征逻辑。")
        return
    
    current_market_status = latest_data['market_status'].iloc[0]
    
    if current_market_status == 1:
        model_type = 'hot'
        print("当前市场状态：热市。加载热市模型...")
    else:
        model_type = 'cold'
        print("当前市场状态：冷市。加载冷市模型...")

    # 尝试加载集成模型，如果不存在则使用单一模型
    ensemble_file = os.path.join(MODEL_DIR, f'lgb_ensemble_{model_type}.joblib')
    model_file = os.path.join(MODEL_DIR, f'lgb_model_{model_type}.joblib')

    use_ensemble = False
    if os.path.exists(ensemble_file):
        print(f"从 {ensemble_file} 加载集成模型...")
        ensemble_models = joblib.load(ensemble_file)
        use_ensemble = True
    elif os.path.exists(model_file):
        print(f"从 {model_file} 加载单一模型...")
        model = joblib.load(model_file)
    else:
        print(f"错误：模型文件不存在。请先运行训练脚本。")
        return

    # 4. 生成预测
    print("生成预测...")
    features = [col for col in latest_data.columns if col not in ['股票代码', '日期', 'target'] and 'prediction' not in col]
    X_test = latest_data[features]

    if use_ensemble:
        # 使用集成模型预测
        print("使用集成模型进行预测...")
        predictions = []
        for i, model in enumerate(ensemble_models):
            pred = model.predict(X_test)
            predictions.append(pred)
        # 取平均值作为最终预测
        latest_data['prediction'] = sum(predictions) / len(predictions)
    else:
        # 使用单一模型预测
        print("使用单一模型进行预测...")
        latest_data['prediction'] = model.predict(X_test)

    # 5. 提取并保存结果到指定格式的文件
    print("提取并保存结果...")

    # 对整个latest_data去重，确保用于nlargest/nsmallest的数据是唯一的
    latest_data.drop_duplicates(subset=['股票代码'], keep='first', inplace=True)

    # 添加预测排名以便更好地选择股票
    latest_data['prediction_rank'] = latest_data['prediction'].rank(ascending=False, method='first')
    latest_data['prediction_rank_asc'] = latest_data['prediction'].rank(ascending=True, method='first')

    print(f"总共有 {len(latest_data)} 支股票的预测结果")
    print(f"预测值范围: {latest_data['prediction'].min():.6f} 到 {latest_data['prediction'].max():.6f}")

    # 涨幅最大的10支股票 - 按预测值降序排列
    top_10_max = latest_data.nlargest(10, 'prediction')[['股票代码', 'prediction']].reset_index(drop=True)
    # 根据官方Q&A (Q7, Q24)，股票代码需要格式化为6位字符串，不足则补0
    top_10_max['股票代码'] = top_10_max['股票代码'].astype(str).str.zfill(6)

    # 涨幅最小的10支股票 - 按预测值升序排列
    top_10_min = latest_data.nsmallest(10, 'prediction')[['股票代码', 'prediction']].reset_index(drop=True)
    # 根据官方Q&A (Q7, Q24)，股票代码需要格式化为6位字符串，不足则补0
    top_10_min['股票代码'] = top_10_min['股票代码'].astype(str).str.zfill(6)

    print("涨幅最大的10支股票:")
    for i, row in top_10_max.iterrows():
        print(f"  {i+1}. {row['股票代码']} (预测值: {row['prediction']:.6f})")

    print("涨幅最小的10支股票:")
    for i, row in top_10_min.iterrows():
        print(f"  {i+1}. {row['股票代码']} (预测值: {row['prediction']:.6f})")

    # 只保留股票代码列用于输出
    top_10_max = top_10_max[['股票代码']]
    top_10_min = top_10_min[['股票代码']]

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 保存为竞赛要求的两个独立CSV文件
    top_10_max_file = os.path.join(OUTPUT_DIR, 'top_10_max_target.csv')
    top_10_min_file = os.path.join(OUTPUT_DIR, 'top_10_min_target.csv')

    # 使用 utf-8-sig 编码以避免在Windows下出现乱码
    top_10_max.to_csv(top_10_max_file, index=False, encoding='utf-8-sig')
    top_10_min.to_csv(top_10_min_file, index=False, encoding='utf-8-sig')

    # 为了兼容性，也保存合并的结果文件
    result_df = pd.concat([top_10_max.rename(columns={'股票代码': '涨幅最大股票代码'}),
                          top_10_min.rename(columns={'股票代码': '涨幅最小股票代码'})], axis=1)
    result_df.to_csv(RESULT_FILE, index=False, encoding='utf-8-sig')

    print(f"涨幅最大股票结果已保存至 {top_10_max_file}")
    print(f"涨幅最小股票结果已保存至 {top_10_min_file}")
    print(f"合并结果已保存至 {RESULT_FILE}")
    print("预测流程结束。")

if __name__ == '__main__':
    generate_predictions() 