import pandas as pd
import joblib
import os
import sys

# 将当前文件所在的目录添加到Python的模块搜索路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from featurework import create_features

# --- Path Setup ---
# 使用绝对路径以避免在不同环境中出现问题
# app/code/src/test.py -> app/
APP_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

DATA_DIR = os.path.join(APP_ROOT, 'data')
MODEL_DIR = os.path.join(APP_ROOT, 'model')
OUTPUT_DIR = os.path.join(APP_ROOT, 'output')
TEST_FILE = os.path.join(DATA_DIR, 'test.csv')
TRAIN_FILE = os.path.join(DATA_DIR, 'train.csv')
# 不再加载单一模型
# MODEL_FILE = os.path.join(MODEL_DIR, 'lgb_model.joblib')
RESULT_FILE = os.path.join(OUTPUT_DIR, 'result.csv') # 最终结果文件

def generate_predictions():
    """
    完整的预测生成流程。
    """
    print("开始生成预测结果...")

    # 1. 加载训练和测试数据
    # 为了给测试集生成有意义的滚动窗口特征，需要利用历史数据
    print(f"从 {TEST_FILE} 和 {TRAIN_FILE} 加载数据...")
    try:
        test_df = pd.read_csv(TEST_FILE, encoding='utf-8')
        train_df = pd.read_csv(TRAIN_FILE, encoding='utf-8')
    except Exception as e:
        print(f"读取数据时出错: {e}")
        return

    # 记录测试集的股票代码和日期，以便后续筛选
    test_identifiers = test_df[['股票代码', '日期']]

    # 合并数据以进行统一的特征工程
    combined_df = pd.concat([train_df, test_df], ignore_index=True)

    # 关键修复：去除可能存在的重复行，确保每个股票在每天只有一条记录
    combined_df.drop_duplicates(subset=['股票代码', '日期'], keep='last', inplace=True)

    # 2. 特征工程
    print("对合并后的数据进行特征工程...")
    # 传递 test_df 的副本以避免 SettingWithCopyWarning
    features_df = create_features(combined_df, is_train=False)

    # 3. 筛选出测试集对应的特征数据
    # 将 test_identifiers 的列转换为 features_df 中匹配的类型
    test_identifiers['日期'] = pd.to_datetime(test_identifiers['日期'])
    features_df['日期'] = pd.to_datetime(features_df['日期'])

    # 使用内连接(merge)来精确地筛选出测试集的数据行
    latest_data = pd.merge(features_df, test_identifiers, on=['股票代码', '日期'], how='inner')

    # 3. 判断当前市场状态并加载相应模型
    # 我们假设最新的数据点能代表当前市场状态
    # 注意：在修复后，latest_data 可能包含多个股票，但它们的market_status应该是一样的
    if latest_data.empty:
        print("错误：在特征工程后，没有找到对应的测试集数据。请检查数据和特征逻辑。")
        return
    
    current_market_status = latest_data['market_status'].iloc[0]
    
    if current_market_status == 1:
        model_type = 'hot'
        print("当前市场状态：热市。加载热市模型...")
    else:
        model_type = 'cold'
        print("当前市场状态：冷市。加载冷市模型...")

    model_file = os.path.join(MODEL_DIR, f'lgb_model_{model_type}.joblib')
    print(f"从 {model_file} 加载模型...")
    if not os.path.exists(model_file):
        print(f"错误：模型文件 {model_file} 不存在。请先运行训练脚本。")
        return
    model = joblib.load(model_file)

    # 4. 生成预测
    print("生成预测...")
    features = [col for col in latest_data.columns if col not in ['股票代码', '日期', 'target'] and 'prediction' not in col]
    X_test = latest_data[features]
    
    latest_data['prediction'] = model.predict(X_test)

    # 5. 提取、合并并保存结果
    print("提取、合并并保存结果...")
    
    # 对整个latest_data去重，确保用于nlargest/nsmallest的数据是唯一的
    latest_data.drop_duplicates(subset=['股票代码'], keep='first', inplace=True)

    # 涨幅最大的10支
    top_10_max = latest_data.nlargest(10, 'prediction')[['股票代码']].reset_index(drop=True)
    top_10_max.columns = ['涨幅最大股票代码']
    # 根据官方Q&A (Q7, Q24)，股票代码需要格式化为6位字符串，不足则补0
    top_10_max['涨幅最大股票代码'] = top_10_max['涨幅最大股票代码'].astype(str).str.zfill(6)
    
    # 涨幅最小的10支
    # 先选出涨幅最小的10支
    top_10_min_df = latest_data.nsmallest(10, 'prediction')
    # 再根据"涨幅第十小到最小"的要求，对这10支股票按预测值降序排列
    top_10_min = top_10_min_df.sort_values('prediction', ascending=False)[['股票代码']].reset_index(drop=True)
    top_10_min.columns = ['涨幅最小股票代码']
    # 根据官方Q&A (Q7, Q24)，股票代码需要格式化为6位字符串，不足则补0
    top_10_min['涨幅最小股票代码'] = top_10_min['涨幅最小股票代码'].astype(str).str.zfill(6)
    
    # 合并成一个DataFrame
    # 根据用户最新确认的规则，使用 [最大, 最小] 的列顺序
    result_df = pd.concat([top_10_max, top_10_min], axis=1)

    os.makedirs(OUTPUT_DIR, exist_ok=True)
    # 使用 utf-8-sig 编码以避免在Windows下出现乱码
    result_df.to_csv(RESULT_FILE, index=False, encoding='utf-8-sig')

    print(f"结果已保存至 {RESULT_FILE}")
    print("预测流程结束。")

if __name__ == '__main__':
    generate_predictions() 