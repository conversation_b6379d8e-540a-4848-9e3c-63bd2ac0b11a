import pandas as pd
import lightgbm as lgb
import optuna
import os
import sys

log_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'output'))
os.makedirs(log_dir, exist_ok=True)
log_file_path = os.path.join(log_dir, 'tuning.log')

if os.path.exists(log_file_path):
    os.remove(log_file_path)

class Logger(object):
    def __init__(self, filename):
        self.terminal = sys.stdout
        self.log = open(filename, "a", encoding='utf-8')

    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)
        self.flush()

    def flush(self):
        self.terminal.flush()
        self.log.flush()

    def __del__(self):
        try:
            self.log.close()
        except:
            pass

sys.stdout = Logger(log_file_path)
print(f"--- Log file is being written to {log_file_path} ---")
# --- End of Logging Code ---


# --- Path Setup ---
# 使用绝对路径以避免在不同环境中出现问题
APP_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from featurework import create_features

DATA_DIR = os.path.join(APP_ROOT, 'data')
TRAIN_FILE = os.path.join(DATA_DIR, 'train.csv')

def objective(trial, market_status_code):
    """
    Optuna的目标函数，用于寻找最佳超参数。
    :param market_status_code: 0代表冷市, 1代表热市。
    """
    # 1. 加载和准备数据
    train_df = pd.read_csv(TRAIN_FILE, encoding='utf-8')
    features_df = create_features(train_df, is_train=True)
    
    # --- 根据市场状态筛选数据 ---
    print(f"\n[试验 {trial.number}] 正在为市场状态 '{'热市' if market_status_code == 1 else '冷市'}' 准备数据...")
    features_df = features_df[features_df['market_status'] == market_status_code].copy()
    print(f"筛选后数据量: {len(features_df)} 行")
    
    if len(features_df) < 1000: # 如果数据太少，无法进行有意义的训练和验证
        print("警告: 筛选后的数据过少，跳过此次试验。")
        return float('inf') # 返回一个无穷大的值，Optuna会忽略这次试验
        
    features_df['日期'] = pd.to_datetime(features_df['日期'])
    features_df = features_df.sort_values(by='日期')
    
    # 使用时间序列进行80/20的划分
    split_point = int(len(features_df) * 0.8)
    train_data = features_df.iloc[:split_point]
    val_data = features_df.iloc[split_point:]

    features = [col for col in features_df.columns if col not in ['股票代码', '日期', 'target']]
    target = 'target'

    X_train = train_data[features]
    y_train = train_data[target]
    X_val = val_data[features]
    y_val = val_data[target]

    # 2. 定义超参数搜索空间
    param = {
        'objective': 'regression_l1',
        'metric': 'rmse',
        'n_estimators': 2000,
        'verbose': -1,
        'n_jobs': -1,
        'seed': 42,
        'boosting_type': 'gbdt',
        'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.1, log=True),
        'num_leaves': trial.suggest_int('num_leaves', 20, 300),
        'max_depth': trial.suggest_int('max_depth', 3, 12),
        'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
        'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
        'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
        'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
        'lambda_l1': trial.suggest_float('lambda_l1', 1e-8, 10.0, log=True),
        'lambda_l2': trial.suggest_float('lambda_l2', 1e-8, 10.0, log=True),
    }

    # 3. 训练模型
    model = lgb.LGBMRegressor(**param)
    model.fit(X_train, y_train,
              eval_set=[(X_val, y_val)],
              eval_metric='rmse',
              callbacks=[lgb.early_stopping(100, verbose=False)])

    # 4. 返回验证集上的分数
    rmse = model.best_score_['valid_0']['rmse']
    print(f"[试验 {trial.number}] 完成。 RMSE: {rmse:.6f}")
    return rmse

def run_tuning(market_status_to_tune):
    """
    执行超参数调优。
    :param market_status_to_tune: 'hot', 'cold', 或 'all'。
    """
    if market_status_to_tune not in ['hot', 'cold', 'all']:
        print("错误: market_status_to_tune 参数必须是 'hot', 'cold', 或 'all' 之一。")
        return

    statuses_to_process = []
    if market_status_to_tune == 'hot':
        statuses_to_process.append(1)
    elif market_status_to_tune == 'cold':
        statuses_to_process.append(0)
    else: # 'all'
        statuses_to_process.extend([1, 0])

    for status_code in statuses_to_process:
        market_type = 'hot' if status_code == 1 else 'cold'
        print(f"\n{'='*50}\n开始为【{market_type.upper()}】市场模型进行超参数调优...\n{'='*50}")
        
        db_path = os.path.join(APP_ROOT, 'output', 'optuna-tuning.db')
        storage_name = f"sqlite:///{db_path}"
        study_name = f"lgbm-tuning-study-{market_type}" # 为不同市场创建不同的研究
        
        print(f"所有试验结果将被保存到: {db_path}")
        print(f"研究名称: {study_name}")
        print("您可以随时使用 Ctrl+C 中断，下次运行时会自动从断点处继续。")
        
        study = optuna.create_study(
            direction='minimize',
            storage=storage_name,
            study_name=study_name,
            load_if_exists=True
        )
        
        # 将市场状态码传递给目标函数
        objective_with_status = lambda trial: objective(trial, status_code)
        
        try:
            # 运行100次试验，您可以根据需要调整这个数字
            study.optimize(objective_with_status, n_trials=100) 
        except KeyboardInterrupt:
            print(f"\n检测到手动停止操作...正在为【{market_type.upper()}】市场输出当前最佳结果...")

        print(f"\n【{market_type.upper()}】市场调优流程结束！")
        print(f"总试验次数: {len(study.trials)}")
        print("最佳试验:")
        best_trial = study.best_trial
        print(f"  数值 (RMSE): {best_trial.value}")
        print("  参数: ")
        for key, value in best_trial.params.items():
            print(f"    '{key}': {value},")

if __name__ == '__main__':
    # --- 在这里选择您想优化的模型 ---
    # 'hot': 只优化热市模型
    # 'cold': 只优化冷市模型
    # 'all': 先优化热市，再优化冷市
    run_tuning('all') 