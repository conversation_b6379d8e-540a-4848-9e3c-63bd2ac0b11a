#!/usr/bin/env python3
"""
大数据挑战赛解决方案验证脚本
验证解决方案是否满足所有竞赛要求
"""

import os
import time
import pandas as pd
import numpy as np
from pathlib import Path

def validate_file_structure():
    """验证文件结构是否符合要求"""
    print("=== 验证文件结构 ===")
    
    required_files = [
        'code/src/train.py',
        'code/src/test.py', 
        'code/src/featurework.py',
        'data/train.csv',
        'data/test.csv'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必需文件: {missing_files}")
        return False
    else:
        print("✅ 所有必需文件都存在")
        return True

def validate_data_format():
    """验证数据格式"""
    print("\n=== 验证数据格式 ===")
    
    try:
        # 检查训练数据
        train_df = pd.read_csv('data/train.csv')
        print(f"✅ 训练数据加载成功: {train_df.shape}")
        
        # 检查测试数据
        test_df = pd.read_csv('data/test.csv')
        print(f"✅ 测试数据加载成功: {test_df.shape}")
        
        # 验证列名
        expected_cols = ['股票代码', '日期', '开盘', '收盘', '最高', '最低', '成交量', '成交额', '振幅', '涨跌额', '换手率', '涨跌幅']
        
        for col in expected_cols:
            if col not in train_df.columns:
                print(f"❌ 训练数据缺少列: {col}")
                return False
            if col not in test_df.columns:
                print(f"❌ 测试数据缺少列: {col}")
                return False
        
        print("✅ 数据列名验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据格式验证失败: {e}")
        return False

def validate_training_time():
    """验证训练时间是否在8小时内"""
    print("\n=== 验证训练时间 ===")
    
    try:
        start_time = time.time()
        
        # 运行训练
        import subprocess
        result = subprocess.run(['python', 'code/src/train.py'], 
                              capture_output=True, text=True, timeout=28800)  # 8小时超时
        
        end_time = time.time()
        training_time = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ 训练成功完成，用时: {training_time/60:.2f} 分钟")
            if training_time <= 28800:  # 8小时 = 28800秒
                print("✅ 训练时间符合要求 (≤8小时)")
                return True
            else:
                print("❌ 训练时间超过8小时限制")
                return False
        else:
            print(f"❌ 训练失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 训练超时 (>8小时)")
        return False
    except Exception as e:
        print(f"❌ 训练验证失败: {e}")
        return False

def validate_prediction_time():
    """验证预测时间是否在5分钟内"""
    print("\n=== 验证预测时间 ===")
    
    try:
        start_time = time.time()
        
        # 运行预测
        import subprocess
        result = subprocess.run(['python', 'code/src/test.py'], 
                              capture_output=True, text=True, timeout=300)  # 5分钟超时
        
        end_time = time.time()
        prediction_time = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ 预测成功完成，用时: {prediction_time:.2f} 秒")
            if prediction_time <= 300:  # 5分钟 = 300秒
                print("✅ 预测时间符合要求 (≤5分钟)")
                return True
            else:
                print("❌ 预测时间超过5分钟限制")
                return False
        else:
            print(f"❌ 预测失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 预测超时 (>5分钟)")
        return False
    except Exception as e:
        print(f"❌ 预测验证失败: {e}")
        return False

def validate_output_format():
    """验证输出格式是否符合要求"""
    print("\n=== 验证输出格式 ===")
    
    try:
        # 检查输出文件是否存在
        max_file = 'output/top_10_max_target.csv'
        min_file = 'output/top_10_min_target.csv'
        
        if not os.path.exists(max_file):
            print(f"❌ 缺少输出文件: {max_file}")
            return False
            
        if not os.path.exists(min_file):
            print(f"❌ 缺少输出文件: {min_file}")
            return False
        
        # 验证文件内容
        max_df = pd.read_csv(max_file)
        min_df = pd.read_csv(min_file)
        
        # 检查列名
        if list(max_df.columns) != ['股票代码']:
            print(f"❌ {max_file} 列名错误: {list(max_df.columns)}")
            return False
            
        if list(min_df.columns) != ['股票代码']:
            print(f"❌ {min_file} 列名错误: {list(min_df.columns)}")
            return False
        
        # 检查行数
        if len(max_df) != 10:
            print(f"❌ {max_file} 行数错误: {len(max_df)} (应为10)")
            return False
            
        if len(min_df) != 10:
            print(f"❌ {min_file} 行数错误: {len(min_df)} (应为10)")
            return False
        
        # 检查股票代码格式
        for idx, code in enumerate(max_df['股票代码']):
            if not isinstance(code, str) or len(code) != 6 or not code.isdigit():
                print(f"❌ {max_file} 第{idx+1}行股票代码格式错误: {code}")
                return False
        
        for idx, code in enumerate(min_df['股票代码']):
            if not isinstance(code, str) or len(code) != 6 or not code.isdigit():
                print(f"❌ {min_file} 第{idx+1}行股票代码格式错误: {code}")
                return False
        
        print("✅ 输出格式验证通过")
        print(f"✅ 涨幅最大10支股票: {list(max_df['股票代码'])}")
        print(f"✅ 涨幅最小10支股票: {list(min_df['股票代码'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 输出格式验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 开始验证大数据挑战赛解决方案...")
    print("=" * 50)
    
    # 切换到正确的工作目录
    os.chdir(Path(__file__).parent.parent.parent)
    
    validation_results = []
    
    # 1. 验证文件结构
    validation_results.append(validate_file_structure())
    
    # 2. 验证数据格式
    validation_results.append(validate_data_format())
    
    # 3. 验证训练时间
    validation_results.append(validate_training_time())
    
    # 4. 验证预测时间
    validation_results.append(validate_prediction_time())
    
    # 5. 验证输出格式
    validation_results.append(validate_output_format())
    
    # 总结
    print("\n" + "=" * 50)
    print("🏁 验证结果总结:")
    
    passed = sum(validation_results)
    total = len(validation_results)
    
    if passed == total:
        print(f"🎉 所有验证通过! ({passed}/{total})")
        print("✅ 解决方案符合所有竞赛要求")
        return True
    else:
        print(f"❌ 部分验证失败 ({passed}/{total})")
        print("⚠️  请修复失败的验证项")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
