import os
import sys

print("--- Starting debug script ---")
print(f"Current working directory: {os.getcwd()}")
print(f"__file__: {__file__}")
abs_path = os.path.abspath(__file__)
print(f"Absolute path of __file__: {abs_path}")
dir_name = os.path.dirname(abs_path)
print(f"Directory name: {dir_name}")

try:
    # Navigate three levels up from the script's directory to reach the 'app' root
    # then go into 'output'
    output_dir = os.path.join(dir_name, '..', '..', 'output')
    os.makedirs(output_dir, exist_ok=True)
    test_file_path = os.path.join(output_dir, 'debug_log.txt')
    print(f"Attempting to write to: {test_file_path}")
    with open(test_file_path, 'w') as f:
        f.write("Debug script was here.")
    print("--- Successfully wrote to debug_log.txt ---")
except Exception as e:
    print(f"An error occurred: {e}")

print("--- Debug script finished ---") 