--- Log file is being written to G:\cursor_大数据挑战赛\app\output\training.log ---
开始模型训练...
从 G:\cursor_大数据挑战赛\app\data\train.csv 加载数据...
开始进行特征工程...
准备训练数据...
热市数据量: 345280, 冷市数据量: 284183

--- 开始训练 HOT 模型 ---
训练集大小: 276399, 验证集大小: 68881
使用参数: {'objective': 'regression_l1', 'metric': 'rmse', 'n_estimators': 2000, 'verbose': -1, 'n_jobs': -1, 'seed': 42, 'boosting_type': 'gbdt', 'learning_rate': 0.027521222584837923, 'num_leaves': 279, 'max_depth': 11, 'min_child_samples': 67, 'feature_fraction': 0.692873781039632, 'bagging_fraction': 0.9763921540274296, 'bagging_freq': 7, 'lambda_l1': 5.6097769887640546e-05, 'lambda_l2': 7.633070303449455e-05}
Training until validation scores don't improve for 100 rounds
Early stopping, best iteration is:
[956]	valid_0's rmse: 0.0998754
训练完成，将模型保存至 G:\cursor_大数据挑战赛\app\model\lgb_model_hot.joblib

--- 开始训练 COLD 模型 ---
训练集大小: 227553, 验证集大小: 56630
使用参数: {'objective': 'regression_l1', 'metric': 'rmse', 'n_estimators': 2000, 'verbose': -1, 'n_jobs': -1, 'seed': 42, 'boosting_type': 'gbdt', 'learning_rate': 0.025004164727326007, 'num_leaves': 237, 'max_depth': 10, 'min_child_samples': 5, 'feature_fraction': 0.76581522048196, 'bagging_fraction': 0.8025584758373271, 'bagging_freq': 2, 'lambda_l1': 0.005139605286676415, 'lambda_l2': 0.03218322390441688}
Training until validation scores don't improve for 100 rounds
Did not meet early stopping. Best iteration is:
[2000]	valid_0's rmse: 0.105659
训练完成，将模型保存至 G:\cursor_大数据挑战赛\app\model\lgb_model_cold.joblib

所有模型训练流程结束。
