--- Log file is being written to G:\cursor_大数据挑战赛\app\output\training.log ---
开始模型训练...
从 G:\cursor_大数据挑战赛\app\data\train.csv 加载数据...
开始进行特征工程...
目标变量处理后统计: 最小值=-3.809637, 最大值=3.827161, 平均值=0.010113
处理NaN前的列数: 47
处理NaN前是否包含股票代码: True
处理NaN后的列数: 47
处理NaN后是否包含股票代码: True
前向填充后是否包含股票代码: True
对 42 个数值特征进行缩放...
最终返回的列: ['股票代码', '日期', '开盘', '收盘', '最高', '最低', '成交量', '成交额', '振幅', '涨跌额', '换手率', '涨跌幅', 'ret', 'MA5', 'MA10', 'MA20', 'VOL20', 'MOMENTUM', 'RSI', 'MACD', 'ret_lag_1', 'ret_lag_2', 'ret_lag_3', 'ret_lag_5', 'ret_lag_10', 'BB_position', 'Williams_R', 'ATR', 'volume_ma5', 'volume_ma20', 'volume_ratio', 'price_position', 'market_status', 'MOMENTUM_rank', 'RSI_rank', 'volume_rank', 'turnover_rank', 'ret_rank', 'buy_pressure', 'sell_pressure', 'price_efficiency', 'relative_strength', 'relative_strength_ma5', 'sector_momentum', 'relative_to_sector', 'sector_encoded', 'target']
准备训练数据...
热市数据量: 345280, 冷市数据量: 284183

--- 开始训练 HOT 模型集成 ---
训练集大小: 276399, 验证集大小: 68881
训练主模型...
训练集成模型 1...
训练集成模型 2...
训练完成，将主模型保存至 G:\cursor_大数据挑战赛\app\model\lgb_model_hot.joblib
集成模型保存至 G:\cursor_大数据挑战赛\app\model\lgb_ensemble_hot.joblib
主模型验证RMSE: 0.094859
集成模型验证RMSE: 0.094816
集成改进: 0.05%

--- 开始训练 COLD 模型集成 ---
训练集大小: 227553, 验证集大小: 56630
训练主模型...
训练集成模型 1...
训练集成模型 2...
训练完成，将主模型保存至 G:\cursor_大数据挑战赛\app\model\lgb_model_cold.joblib
集成模型保存至 G:\cursor_大数据挑战赛\app\model\lgb_ensemble_cold.joblib
主模型验证RMSE: 0.097823
集成模型验证RMSE: 0.097686
集成改进: 0.14%

所有模型训练流程结束。
