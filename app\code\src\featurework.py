import pandas as pd

def create_features(df, is_train=True):
    """
    根据输入的DataFrame创建新的技术指标特征。

    :param df: 包含股票数据的pandas DataFrame。
    :param is_train:布尔值，表示是否为训练模式。如果是，则会创建目标变量。
    :return: 带有新特征的DataFrame。
    """
    # 确保数据按股票和日期排序
    df = df.sort_values(by=['股票代码', '日期']).reset_index(drop=True)
    
    # 计算一些基础指标
    df['ret'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.pct_change())

    # 计算移动平均线 (Moving Averages)
    df['MA5'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.rolling(window=5).mean())
    df['MA10'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.rolling(window=10).mean())
    df['MA20'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.rolling(window=20).mean())

    # 计算波动率 (Volatility)
    df['VOL20'] = df.groupby('股票代码')['ret'].transform(lambda x: x.rolling(window=20).std())
    
    # 计算动量 (Momentum)
    df['MOMENTUM'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.diff(5))

    # 计算RSI
    def rsi(x, window=14):
        delta = x.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    df['RSI'] = df.groupby('股票代码')['收盘'].transform(lambda x: rsi(x))

    # 计算MACD
    def macd(x, fast=12, slow=26, signal=9):
        exp1 = x.ewm(span=fast, adjust=False).mean()
        exp2 = x.ewm(span=slow, adjust=False).mean()
        macd_line = exp1 - exp2
        signal_line = macd_line.ewm(span=signal, adjust=False).mean()
        return macd_line - signal_line
    df['MACD'] = df.groupby('股票代码')['收盘'].transform(lambda x: macd(x))

    # 创建滞后特征
    for lag in [1, 2, 3, 5]:
        df[f'ret_lag_{lag}'] = df.groupby('股票代码')['ret'].transform(lambda x: x.shift(lag))
    
    # --- 宏观市场状态特征 ---
    # 计算每日所有股票的平均收盘价，作为一个简单的市场温度计
    market_mean_close = df.groupby('日期')['收盘'].transform('mean')
    # 计算市场温度的长期移动平均线
    market_ma = market_mean_close.rolling(window=20, min_periods=1).mean()
    # 定义市场状态：当天的市场均价高于长期均线时，认为是"热市" (1)，否则为"冷市" (0)
    df['market_status'] = (market_mean_close > market_ma).astype(int)

    # --- 排名特征 ---
    # 计算关键指标在每日的截面排名（百分位），赋予模型相对强弱信息
    df['MOMENTUM_rank'] = df.groupby('日期')['MOMENTUM'].rank(pct=True)
    df['RSI_rank'] = df.groupby('日期')['RSI'].rank(pct=True)

    if is_train:
        # 仅在训练模式下创建目标变量
        # 根据赛题描述和数据结构分析，目标是预测未来第1个交易日，因此使用shift(-1)
        df['target'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.shift(-1) / x - 1)

    # 统一训练和测试的NaN处理逻辑，避免因dropna()丢失过多训练数据
    # MA20是窗口最大的特征，因此以它为基准。
    df = df.dropna(subset=['MA5', 'MA10', 'VOL20', 'MOMENTUM', 'RSI', 'MACD'])

    return df

if __name__ == '__main__':
    # 此处用于单元测试
    # 创建一个示例DataFrame来测试函数
    data = {
        '股票代码': ['000001'] * 30,
        '日期': pd.to_datetime(pd.date_range(start='2025-01-01', periods=30)),
        '收盘': range(10, 40)
    }
    sample_df = pd.DataFrame(data)
    
    # 测试训练模式
    print("--- 测试训练模式 ---")
    features_df_train = create_features(sample_df.copy(), is_train=True)
    print(features_df_train.tail())
    print("列:", features_df_train.columns)
    
    # 测试预测模式
    print("\n--- 测试预测模式 ---")
    features_df_test = create_features(sample_df.copy(), is_train=False)
    print(features_df_test.tail())
    print("列:", features_df_test.columns) 