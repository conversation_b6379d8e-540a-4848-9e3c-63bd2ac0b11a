import pandas as pd
from sklearn.preprocessing import LabelEncoder

def create_features(df, is_train=True):
    """
    根据输入的DataFrame创建新的技术指标特征。

    :param df: 包含股票数据的pandas DataFrame。
    :param is_train:布尔值，表示是否为训练模式。如果是，则会创建目标变量。
    :return: 带有新特征的DataFrame。
    """
    # 确保数据按股票和日期排序
    df = df.sort_values(by=['股票代码', '日期']).reset_index(drop=True)
    
    # 计算一些基础指标
    df['ret'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.pct_change())

    # 计算移动平均线 (Moving Averages)
    df['MA5'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.rolling(window=5).mean())
    df['MA10'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.rolling(window=10).mean())
    df['MA20'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.rolling(window=20).mean())

    # 计算波动率 (Volatility)
    df['VOL20'] = df.groupby('股票代码')['ret'].transform(lambda x: x.rolling(window=20).std())
    
    # 计算动量 (Momentum)
    df['MOMENTUM'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.diff(5))

    # 计算RSI
    def rsi(x, window=14):
        delta = x.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    df['RSI'] = df.groupby('股票代码')['收盘'].transform(lambda x: rsi(x))

    # 计算MACD
    def macd(x, fast=12, slow=26, signal=9):
        exp1 = x.ewm(span=fast, adjust=False).mean()
        exp2 = x.ewm(span=slow, adjust=False).mean()
        macd_line = exp1 - exp2
        signal_line = macd_line.ewm(span=signal, adjust=False).mean()
        return macd_line - signal_line
    df['MACD'] = df.groupby('股票代码')['收盘'].transform(lambda x: macd(x))

    # 创建滞后特征
    for lag in [1, 2, 3, 5, 10]:
        df[f'ret_lag_{lag}'] = df.groupby('股票代码')['ret'].transform(lambda x: x.shift(lag))

    # 计算布林带 (Bollinger Bands)
    def bollinger_bands(x, window=20, num_std=2):
        rolling_mean = x.rolling(window=window).mean()
        rolling_std = x.rolling(window=window).std()
        upper_band = rolling_mean + (rolling_std * num_std)
        lower_band = rolling_mean - (rolling_std * num_std)
        bb_position = (x - lower_band) / (upper_band - lower_band)
        return bb_position

    df['BB_position'] = df.groupby('股票代码')['收盘'].transform(lambda x: bollinger_bands(x))

    # 计算威廉指标 (Williams %R) - 简化版本
    df['highest_14'] = df.groupby('股票代码')['最高'].transform(lambda x: x.rolling(window=14).max())
    df['lowest_14'] = df.groupby('股票代码')['最低'].transform(lambda x: x.rolling(window=14).min())
    df['Williams_R'] = -100 * (df['highest_14'] - df['收盘']) / (df['highest_14'] - df['lowest_14'])
    df.drop(['highest_14', 'lowest_14'], axis=1, inplace=True)

    # 计算平均真实波幅 (ATR) - 简化版本
    df['prev_close'] = df.groupby('股票代码')['收盘'].shift(1)
    df['tr1'] = df['最高'] - df['最低']
    df['tr2'] = abs(df['最高'] - df['prev_close'])
    df['tr3'] = abs(df['最低'] - df['prev_close'])
    df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    df['ATR'] = df.groupby('股票代码')['true_range'].transform(lambda x: x.rolling(window=14).mean())
    df.drop(['prev_close', 'tr1', 'tr2', 'tr3', 'true_range'], axis=1, inplace=True)

    # 计算成交量相关指标
    df['volume_ma5'] = df.groupby('股票代码')['成交量'].transform(lambda x: x.rolling(window=5).mean())
    df['volume_ma20'] = df.groupby('股票代码')['成交量'].transform(lambda x: x.rolling(window=20).mean())
    df['volume_ratio'] = df['成交量'] / df['volume_ma20']

    # 计算价格相对位置
    df['price_position'] = df.groupby('股票代码')['收盘'].transform(
        lambda x: (x - x.rolling(window=20).min()) / (x.rolling(window=20).max() - x.rolling(window=20).min())
    )
    
    # --- 宏观市场状态特征 ---
    # 计算每日所有股票的平均收盘价，作为一个简单的市场温度计
    market_mean_close = df.groupby('日期')['收盘'].transform('mean')
    # 计算市场温度的长期移动平均线
    market_ma = market_mean_close.rolling(window=20, min_periods=1).mean()
    # 定义市场状态：当天的市场均价高于长期均线时，认为是"热市" (1)，否则为"冷市" (0)
    df['market_status'] = (market_mean_close > market_ma).astype(int)

    # --- 排名特征 ---
    # 计算关键指标在每日的截面排名（百分位），赋予模型相对强弱信息
    df['MOMENTUM_rank'] = df.groupby('日期')['MOMENTUM'].rank(pct=True)
    df['RSI_rank'] = df.groupby('日期')['RSI'].rank(pct=True)
    df['volume_rank'] = df.groupby('日期')['成交量'].rank(pct=True)
    df['turnover_rank'] = df.groupby('日期')['换手率'].rank(pct=True)
    df['ret_rank'] = df.groupby('日期')['ret'].rank(pct=True)

    # --- 市场微观结构特征 ---
    # 计算买卖压力指标
    df['buy_pressure'] = (df['收盘'] - df['最低']) / (df['最高'] - df['最低'])
    df['sell_pressure'] = (df['最高'] - df['收盘']) / (df['最高'] - df['最低'])

    # 计算价格效率指标
    df['price_efficiency'] = abs(df['收盘'] - df['开盘']) / df['振幅']

    # 计算相对强弱指标
    market_ret = df.groupby('日期')['ret'].transform('mean')
    df['relative_strength'] = df['ret'] - market_ret
    df['relative_strength_ma5'] = df.groupby('股票代码')['relative_strength'].transform(lambda x: x.rolling(window=5).mean())

    # 计算行业动量（简化版，基于股票代码前缀）
    df['sector'] = df['股票代码'].astype(str).str[:3]
    sector_ret = df.groupby(['日期', 'sector'])['ret'].transform('mean')
    df['sector_momentum'] = sector_ret
    df['relative_to_sector'] = df['ret'] - df['sector_momentum']

    # 将sector编码为数值型
    le = LabelEncoder()
    df['sector_encoded'] = le.fit_transform(df['sector'])
    df.drop('sector', axis=1, inplace=True)  # 删除原始的字符串列

    if is_train:
        # 仅在训练模式下创建目标变量
        # 根据赛题描述，目标是预测未来第5个交易日的价格变化，因此使用shift(-5)
        df['target'] = df.groupby('股票代码')['收盘'].transform(lambda x: x.shift(-5) / x - 1)

    # 统一训练和测试的NaN处理逻辑，避免因dropna()丢失过多训练数据
    # 删除关键特征为NaN的行，保留足够的训练数据
    key_features = ['MA5', 'MA10', 'MA20', 'VOL20', 'MOMENTUM', 'RSI', 'MACD', 'BB_position', 'Williams_R', 'ATR']
    df = df.dropna(subset=key_features)

    # 对剩余的NaN值进行前向填充
    df = df.groupby('股票代码').ffill()

    return df

if __name__ == '__main__':
    # 此处用于单元测试
    # 创建一个示例DataFrame来测试函数
    data = {
        '股票代码': ['000001'] * 30,
        '日期': pd.to_datetime(pd.date_range(start='2025-01-01', periods=30)),
        '开盘': range(10, 40),
        '收盘': range(10, 40),
        '最高': [x + 0.5 for x in range(10, 40)],
        '最低': [x - 0.5 for x in range(10, 40)],
        '成交量': [1000000] * 30,
        '成交额': [10000000] * 30,
        '振幅': [2.0] * 30,
        '涨跌额': [0.1] * 30,
        '换手率': [1.5] * 30,
        '涨跌幅': [1.0] * 30
    }
    sample_df = pd.DataFrame(data)
    
    # 测试训练模式
    print("--- 测试训练模式 ---")
    features_df_train = create_features(sample_df.copy(), is_train=True)
    print(features_df_train.tail())
    print("列:", features_df_train.columns)
    
    # 测试预测模式
    print("\n--- 测试预测模式 ---")
    features_df_test = create_features(sample_df.copy(), is_train=False)
    print(features_df_test.tail())
    print("列:", features_df_test.columns) 