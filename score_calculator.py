import pandas as pd
from scipy.stats import spearmanr
import os

# --- 配置 ---
# 假设您的文件都存放在 app/output/ 目录下
OUTPUT_DIR = os.path.join('app', 'output')
PREDICTED_FILE = os.path.join(OUTPUT_DIR, 'result.csv')
ACTUAL_FILE = os.path.join(OUTPUT_DIR, 'actual_result.csv') # 您需要提供的真实结果文件

def calculate_f1_score(predicted_list, actual_list):
    """
    计算F1分数。
    在这个场景下，预测数量和实际数量都是10，F1分数可以简化为 命中数量 / 10。
    """
    if not isinstance(predicted_list, list):
        predicted_list = list(predicted_list)
    if not isinstance(actual_list, list):
        actual_list = list(actual_list)
        
    hits = len(set(predicted_list) & set(actual_list))
    f1 = hits / 10.0
    return f1, hits

def calculate_rank_correlation(predicted_list, actual_list):
    """
    计算Spearman秩相关系数。
    对于实际列表中的每一项，我们查找它在预测列表中的排名。
    如果未在预测列表中找到，则给予一个惩罚性排名(这里是11)。
    """
    if not isinstance(predicted_list, list):
        predicted_list = list(predicted_list)
    if not isinstance(actual_list, list):
        actual_list = list(actual_list)

    # 将预测列表转换为 股票代码 -> 排名 的字典，以便快速查找
    pred_rank_map = {stock: rank + 1 for rank, stock in enumerate(predicted_list)}

    actual_ranks = list(range(1, 11))
    predicted_ranks = []

    for stock in actual_list:
        # 如果在预测列表里，就用它的排名；否则，用惩罚排名11
        rank = pred_rank_map.get(stock, 11)
        predicted_ranks.append(rank)

    # 使用scipy计算Spearman相关性
    correlation, _ = spearmanr(actual_ranks, predicted_ranks)
    return correlation, actual_ranks, predicted_ranks

def main():
    """
    主函数，用于加载数据、计算并打印分数。
    """
    print("开始计算最终得分...")

    # 检查文件是否存在
    if not os.path.exists(PREDICTED_FILE):
        print(f"错误: 预测文件未找到于 {PREDICTED_FILE}")
        return
    if not os.path.exists(ACTUAL_FILE):
        print(f"错误: 真实结果文件未找到于 {ACTUAL_FILE}")
        print("请将包含真实排名的CSV文件命名为 'actual_result.csv' 并放入 'app/output' 文件夹中。")
        print("该文件的格式应与 result.csv 相同，包含'涨幅最大股票代码'和'涨幅最小股票代码'两列。")
        return

    # 加载数据
    try:
        pred_df = pd.read_csv(PREDICTED_FILE, encoding='utf-8-sig')
        actual_df = pd.read_csv(ACTUAL_FILE, encoding='utf-8-sig')
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return
        
    # 确保列名正确
    required_cols = ['涨幅最大股票代码', '涨幅最小股票代码']
    if not all(col in pred_df.columns for col in required_cols) or not all(col in actual_df.columns for col in required_cols):
        print(f"错误：CSV文件必须包含以下列: {required_cols}")
        return


    # 提取列表
    pred_up = pred_df['涨幅最大股票代码'].tolist()
    pred_down = pred_df['涨幅最小股票代码'].tolist()
    actual_up = actual_df['涨幅最大股票代码'].tolist()
    actual_down = actual_df['涨幅最小股票代码'].tolist()

    # --- 计算各项得分 ---
    f1_up, hits_up = calculate_f1_score(pred_up, actual_up)
    f1_down, hits_down = calculate_f1_score(pred_down, actual_down)

    rank_corr_up, _, _ = calculate_rank_correlation(pred_up, actual_up)
    rank_corr_down, _, _ = calculate_rank_correlation(pred_down, actual_down)

    # --- 计算最终得分 ---
    final_score = (0.2 * f1_up) + (0.2 * f1_down) + (0.3 * rank_corr_up) + (0.3 * rank_corr_down)

    # --- 打印结果 ---
    print("\n--- 得分详情 ---")
    print(f"涨幅最大股票(Top 10):")
    print(f"  - 命中数量: {hits_up} / 10")
    print(f"  - F1 Score (F1up): {f1_up:.4f}")
    print(f"  - 排名相关性 (Rank Correlation_up): {rank_corr_up:.4f}")
    
    print(f"\n涨幅最小股票(Bottom 10):")
    print(f"  - 命中数量: {hits_down} / 10")
    print(f"  - F1 Score (F1down): {f1_down:.4f}")
    print(f"  - 排名相关性 (Rank Correlation_down): {rank_corr_down:.4f}")

    print("\n" + "="*20)
    print(f"最终得分: {final_score:.4f}")
    print("="*20)


if __name__ == '__main__':
    main() 