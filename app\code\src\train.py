import pandas as pd
import lightgbm as lgb
import joblib
import os
import sys

# --- Start of Logging Code ---
# Get absolute path for the log directory to avoid issues with chdir
log_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'output'))
os.makedirs(log_dir, exist_ok=True)
log_file_path = os.path.join(log_dir, 'training.log')

# Clear log file if it exists
if os.path.exists(log_file_path):
    os.remove(log_file_path)

class Logger(object):
    def __init__(self, filename):
        self.terminal = sys.stdout
        self.log = open(filename, "a", encoding='utf-8')

    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)
        self.flush() # Flush after each write

    def flush(self):
        self.terminal.flush()
        self.log.flush()

    def __del__(self):
        try:
            self.log.close()
        except:
            pass

sys.stdout = Logger(log_file_path)
print(f"--- Log file is being written to {log_file_path} ---")
# --- End of Logging Code ---

# 将当前文件所在的目录添加到Python的模块搜索路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from featurework import create_features

# --- Path Setup ---
# 使用绝对路径以避免在不同环境中出现问题
# app/code/src/train.py -> app/
APP_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

DATA_DIR = os.path.join(APP_ROOT, 'data')
MODEL_DIR = os.path.join(APP_ROOT, 'model')
TRAIN_FILE = os.path.join(DATA_DIR, 'train.csv')
# 不再保存单一模型，而是根据市场状态保存两个模型
# MODEL_FILE = os.path.join(MODEL_DIR, 'lgb_model.joblib')

def train_model():
    """
    完整的模型训练流程。
    """
    print("开始模型训练...")

    # 1. 加载数据
    print(f"从 {TRAIN_FILE} 加载数据...")
    try:
        # 根据报错信息，文件的编码不是gbk，尝试使用utf-8
        train_df = pd.read_csv(TRAIN_FILE, encoding='utf-8')
    except Exception as e:
        print(f"读取训练数据时出错: {e}")
        return

    # 2. 特征工程
    print("开始进行特征工程...")
    features_df = create_features(train_df, is_train=True)
    
    # 将'日期'列转换为datetime对象，以便于按时间拆分
    features_df['日期'] = pd.to_datetime(features_df['日期'])
    
    # 3. 准备训练数据
    print("准备训练数据...")
    # 按日期排序，并找到80%的分割点
    features_df = features_df.sort_values(by='日期')

    # --- 根据市场状态划分数据 ---
    hot_market_df = features_df[features_df['market_status'] == 1]
    cold_market_df = features_df[features_df['market_status'] == 0]

    print(f"热市数据量: {len(hot_market_df)}, 冷市数据量: {len(cold_market_df)}")

    # 定义要训练的模型和对应的数据
    models_to_train = {
        'hot': hot_market_df,
        'cold': cold_market_df
    }

    # 定义特征列和目标列
    features = [col for col in features_df.columns if col not in ['股票代码', '日期', 'target']]
    target = 'target'

    # 为HOT市场和COLD市场定义各自的最佳参数
    base_params = {
        'objective': 'regression',  # 改为regression以更好地处理连续值预测
        'metric': 'rmse',
        'n_estimators': 3000,  # 增加树的数量
        'verbose': -1,
        'n_jobs': -1,
        'seed': 42,
        'boosting_type': 'gbdt',
        'force_col_wise': True,  # 提高训练速度
    }

    # 更新的超参数，基于新的特征集优化
    lgb_params_hot = {
        **base_params,
        'learning_rate': 0.02,
        'num_leaves': 300,
        'max_depth': 12,
        'min_child_samples': 50,
        'feature_fraction': 0.7,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'lambda_l1': 0.001,
        'lambda_l2': 0.001,
        'min_gain_to_split': 0.01,
    }

    lgb_params_cold = {
        **base_params,
        'learning_rate': 0.02,
        'num_leaves': 250,
        'max_depth': 10,
        'min_child_samples': 30,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 3,
        'lambda_l1': 0.01,
        'lambda_l2': 0.01,
        'min_gain_to_split': 0.01,
    }

    # 训练多个模型进行集成
    ensemble_models = {}

    for model_type, data in models_to_train.items():
        print(f"\n--- 开始训练 {model_type.upper()} 模型集成 ---")

        # 准备训练和验证数据
        data = data.sort_values(by='日期')
        split_date = data['日期'].quantile(0.8, interpolation='nearest')
        train_data = data[data['日期'] <= split_date]
        val_data = data[data['日期'] > split_date]

        X_train = train_data[features]
        y_train = train_data[target]
        X_val = val_data[features]
        y_val = val_data[target]

        print(f"训练集大小: {len(train_data)}, 验证集大小: {len(val_data)}")

        # 根据模型类型选择参数
        if model_type == 'hot':
            lgb_params = lgb_params_hot
        else: # 'cold'
            lgb_params = lgb_params_cold

        # 训练多个模型进行集成
        models_ensemble = []

        # 主模型
        print(f"训练主模型...")
        main_model = lgb.LGBMRegressor(**lgb_params)
        main_model.fit(X_train, y_train,
                      eval_set=[(X_val, y_val)],
                      eval_metric='rmse',
                      callbacks=[lgb.early_stopping(150, verbose=False)])
        models_ensemble.append(main_model)

        # 训练额外的模型用于集成（使用不同的随机种子）
        for i in range(2):
            print(f"训练集成模型 {i+1}...")
            ensemble_params = lgb_params.copy()
            ensemble_params['seed'] = 42 + i + 1
            ensemble_params['feature_fraction'] = max(0.6, lgb_params['feature_fraction'] - 0.1 * i)
            ensemble_params['bagging_fraction'] = max(0.7, lgb_params['bagging_fraction'] - 0.05 * i)

            ensemble_model = lgb.LGBMRegressor(**ensemble_params)
            ensemble_model.fit(X_train, y_train,
                              eval_set=[(X_val, y_val)],
                              eval_metric='rmse',
                              callbacks=[lgb.early_stopping(150, verbose=False)])
            models_ensemble.append(ensemble_model)

        # 保存集成模型
        ensemble_models[model_type] = models_ensemble
        model_file = os.path.join(MODEL_DIR, f'lgb_model_{model_type}.joblib')
        print(f"训练完成，将主模型保存至 {model_file}")
        joblib.dump(main_model, model_file)

        # 保存集成模型
        ensemble_file = os.path.join(MODEL_DIR, f'lgb_ensemble_{model_type}.joblib')
        print(f"集成模型保存至 {ensemble_file}")
        joblib.dump(models_ensemble, ensemble_file)

        # 评估集成效果
        ensemble_pred = sum(model.predict(X_val) for model in models_ensemble) / len(models_ensemble)
        main_pred = main_model.predict(X_val)

        from sklearn.metrics import mean_squared_error
        import numpy as np
        ensemble_rmse = np.sqrt(mean_squared_error(y_val, ensemble_pred))
        main_rmse = np.sqrt(mean_squared_error(y_val, main_pred))

        print(f"主模型验证RMSE: {main_rmse:.6f}")
        print(f"集成模型验证RMSE: {ensemble_rmse:.6f}")
        print(f"集成改进: {((main_rmse - ensemble_rmse) / main_rmse * 100):.2f}%")

    print("\n所有模型训练流程结束。")

if __name__ == '__main__':
    # 注意：直接运行此脚本需要确保 'train_大数据挑战赛.csv' 文件在根目录
    # 并且 'featurework.py' 在同一个目录下或在Python路径中
    train_model() 